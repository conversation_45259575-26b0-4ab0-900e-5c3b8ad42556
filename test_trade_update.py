#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试update_position_by_trade功能
包括同一order_id多次trade和不同order_id的场景
"""

import requests
import json
import time
from datetime import datetime
from position_client_demo import PositionReportClient


def test_trade_update_functionality():
    """测试trade更新功能的完整场景"""
    print("🧪 测试update_position_by_trade功能")
    print("=" * 60)
    
    # 使用live-analyze机器的地址
    base_url = "http://172.31.43.182:5000"
    exchange = "test_exchange"
    symbol = "BTCUSDT"
    position_side = "LONG"
    
    # 创建客户端
    client = PositionReportClient(exchange, base_url)
    
    print("📋 测试场景:")
    print("  1. 先开仓")
    print("  2. 同一order_id多次trade累计")
    print("  3. 不同order_id的trade")
    print("  4. 验证累计数量计算正确性")
    print("=" * 60)
    
    # 清除所有数据
    print("🧹 清除所有现有数据...")
    clear_response = requests.post(f"{base_url}/api/positions/clear")
    if clear_response.status_code == 200:
        print("✅ 数据清除成功")
    else:
        print(f"❌ 数据清除失败: {clear_response.text}")
        return False
    
    time.sleep(1)
    
    # 1. 先开仓
    print("\n1️⃣ 开仓测试")
    print("-" * 40)
    
    open_price = 50000.0
    target_quantity = 1.0
    
    success = client.open_position(
        symbol=symbol,
        position_side=position_side,
        open_price=open_price,
        target_quantity=target_quantity
    )
    
    if not success:
        print("❌ 开仓失败")
        return False
    
    print(f"✅ 开仓成功: {symbol} {position_side}, 目标数量: {target_quantity}")
    time.sleep(1)
    
    # 2. 测试同一order_id多次trade
    print("\n2️⃣ 同一order_id多次trade测试")
    print("-" * 40)
    
    order_id_1 = "ORDER_001"
    
    # 第一次trade
    print(f"第一次trade: order_id={order_id_1}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=50100.0,
        acc_quantity=0.3
    )
    
    if not success:
        print("❌ 第一次trade失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 第一次trade后当前数量: {current_qty}")
        expected_qty = 0.3
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 数量正确")
        else:
            print(f"❌ 数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 第二次trade (同一order_id，累计数量增加)
    print(f"\n第二次trade: order_id={order_id_1} (累计数量增加)")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=50150.0,
        acc_quantity=0.5  # 累计数量从0.3增加到0.5
    )
    
    if not success:
        print("❌ 第二次trade失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 第二次trade后当前数量: {current_qty}")
        expected_qty = 0.5  # 应该是0.5，因为实际增加了0.2 (0.5-0.3)
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 累计数量计算正确")
        else:
            print(f"❌ 累计数量计算错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 3. 测试不同order_id的trade
    print("\n3️⃣ 不同order_id的trade测试")
    print("-" * 40)
    
    order_id_2 = "ORDER_002"
    
    print(f"新order_id trade: order_id={order_id_2}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_2,
        position_side=position_side,
        price=50200.0,
        acc_quantity=0.3  # 新订单的累计数量
    )
    
    if not success:
        print("❌ 新order_id trade失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 新order_id trade后当前数量: {current_qty}")
        expected_qty = 0.8  # 0.5 + 0.3 = 0.8
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 不同order_id累计正确")
        else:
            print(f"❌ 不同order_id累计错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 4. 再次测试第一个order_id
    print("\n4️⃣ 再次测试第一个order_id")
    print("-" * 40)
    
    print(f"第三次trade: order_id={order_id_1} (累计数量再次增加)")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=50250.0,
        acc_quantity=0.7  # 累计数量从0.5增加到0.7
    )
    
    if not success:
        print("❌ 第三次trade失败")
        return False
    
    time.sleep(1)
    
    # 最终检查
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 最终当前数量: {current_qty}")
        expected_qty = 1.0  # 0.8 + 0.2 (0.7-0.5) = 1.0
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 最终累计数量正确")
        else:
            print(f"❌ 最终累计数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    print("\n🎉 trade更新功能测试完成！")
    return True


def test_error_scenarios():
    """测试错误场景"""
    print("\n🧪 测试错误场景")
    print("=" * 60)
    
    base_url = "http://172.31.43.182:5000"
    exchange = "test_exchange"
    symbol = "ETHUSDT"
    position_side = "SHORT"
    
    client = PositionReportClient(exchange, base_url)
    
    # 测试未开仓就trade
    print("1️⃣ 测试未开仓就trade")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id="ORDER_ERROR",
        position_side=position_side,
        price=3000.0,
        acc_quantity=1.0
    )
    
    if not success:
        print("✅ 正确拒绝了未开仓的trade更新")
    else:
        print("❌ 应该拒绝未开仓的trade更新")
    
    print("\n🎉 错误场景测试完成！")


if __name__ == "__main__":
    print("🚀 开始测试update_position_by_trade功能")
    print("目标服务器: http://172.31.43.182:5000")
    print("=" * 60)
    
    # 测试主要功能
    success = test_trade_update_functionality()
    
    if success:
        # 测试错误场景
        test_error_scenarios()
        print("\n✅ 所有测试完成！")
    else:
        print("\n❌ 主要功能测试失败！")
