#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试update_position_by_trade的减仓功能
包括负数acc_quantity的处理
"""

import requests
import json
import time
from datetime import datetime
from position_client_demo import PositionReportClient


def test_reduce_position_functionality():
    """测试减仓功能的完整场景"""
    print("🧪 测试update_position_by_trade减仓功能")
    print("=" * 60)
    
    # 使用live-analyze机器的公网地址
    base_url = "http://18.183.131.174:5000"
    exchange = "test_exchange"
    symbol = "ETHUSDT"
    position_side = "LONG"
    
    # 创建客户端
    client = PositionReportClient(exchange, base_url)
    
    print("📋 测试场景:")
    print("  1. 先开仓")
    print("  2. 增仓到一定数量")
    print("  3. 减仓操作（负数acc_quantity）")
    print("  4. 验证减仓计算正确性")
    print("=" * 60)
    
    # 清除所有数据
    print("🧹 清除所有现有数据...")
    clear_response = requests.post(f"{base_url}/api/positions/clear")
    if clear_response.status_code == 200:
        print("✅ 数据清除成功")
    else:
        print(f"❌ 数据清除失败: {clear_response.text}")
        return False
    
    time.sleep(1)
    
    # 1. 先开仓
    print("\n1️⃣ 开仓测试")
    print("-" * 40)
    
    open_price = 3000.0
    target_quantity = 2.0
    
    success = client.open_position(
        symbol=symbol,
        position_side=position_side,
        open_price=open_price,
        target_quantity=target_quantity
    )
    
    if not success:
        print("❌ 开仓失败")
        return False
    
    print(f"✅ 开仓成功: {symbol} {position_side}, 目标数量: {target_quantity}")
    time.sleep(1)
    
    # 2. 增仓操作
    print("\n2️⃣ 增仓操作")
    print("-" * 40)
    
    order_id_1 = "ORDER_LONG_001"
    
    # 第一次增仓
    print(f"第一次增仓: order_id={order_id_1}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=3050.0,
        acc_quantity=1.0  # 增仓1.0
    )
    
    if not success:
        print("❌ 第一次增仓失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 第一次增仓后当前数量: {current_qty}")
        expected_qty = 1.0
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 增仓数量正确")
        else:
            print(f"❌ 增仓数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 第二次增仓
    print(f"\n第二次增仓: order_id={order_id_1}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=3080.0,
        acc_quantity=1.5  # 累计增仓1.5，实际增加0.5
    )
    
    if not success:
        print("❌ 第二次增仓失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 第二次增仓后当前数量: {current_qty}")
        expected_qty = 1.5
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 累计增仓数量正确")
        else:
            print(f"❌ 累计增仓数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 3. 减仓操作
    print("\n3️⃣ 减仓操作测试")
    print("-" * 40)
    
    # 第一次减仓
    print(f"第一次减仓: order_id={order_id_1}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_1,
        position_side=position_side,
        price=3100.0,
        acc_quantity=1.0  # 累计数量从1.5减少到1.0，实际减少0.5
    )
    
    if not success:
        print("❌ 第一次减仓失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 第一次减仓后当前数量: {current_qty}")
        expected_qty = 1.0  # 1.5 - 0.5 = 1.0
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 减仓数量正确")
        else:
            print(f"❌ 减仓数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 4. 测试负数累计数量（直接减仓）
    print("\n4️⃣ 负数累计数量减仓测试")
    print("-" * 40)
    
    order_id_2 = "ORDER_REDUCE_001"
    
    print(f"负数减仓: order_id={order_id_2}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_2,
        position_side=position_side,
        price=3120.0,
        acc_quantity=-0.3  # 负数表示减仓0.3
    )
    
    if not success:
        print("❌ 负数减仓失败")
        return False
    
    time.sleep(1)
    
    # 检查仓位
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 负数减仓后当前数量: {current_qty}")
        expected_qty = 0.7  # 1.0 - 0.3 = 0.7
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 负数减仓数量正确")
        else:
            print(f"❌ 负数减仓数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    # 5. 继续负数减仓
    print(f"\n继续负数减仓: order_id={order_id_2}")
    success = client.update_position_by_trade(
        symbol=symbol,
        order_id=order_id_2,
        position_side=position_side,
        price=3140.0,
        acc_quantity=-0.5  # 累计减仓从-0.3到-0.5，实际再减少0.2
    )
    
    if not success:
        print("❌ 继续负数减仓失败")
        return False
    
    time.sleep(1)
    
    # 最终检查
    position_data = client.get_position_data(symbol, position_side)
    if position_data:
        current_qty = float(position_data.get("current_quantity", 0))
        print(f"📊 最终当前数量: {current_qty}")
        expected_qty = 0.5  # 0.7 - 0.2 = 0.5
        if abs(current_qty - expected_qty) < 0.001:
            print("✅ 最终减仓数量正确")
        else:
            print(f"❌ 最终减仓数量错误，期望: {expected_qty}, 实际: {current_qty}")
    
    print("\n🎉 减仓功能测试完成！")
    return True


if __name__ == "__main__":
    print("🚀 开始测试减仓功能")
    print("目标服务器: http://18.183.131.174:5000")
    print("=" * 60)
    
    # 测试减仓功能
    success = test_reduce_position_functionality()
    
    if success:
        print("\n✅ 减仓功能测试成功！")
    else:
        print("\n❌ 减仓功能测试失败！")
