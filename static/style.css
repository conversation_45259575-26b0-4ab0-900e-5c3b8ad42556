/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 15px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

header h1 {
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

.status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

#connection-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-connected {
    background: #27ae60;
    color: white;
}

.status-disconnected {
    background: #e74c3c;
    color: white;
}

#last-update {
    font-size: 12px;
    color: #7f8c8d;
}

/* 主内容区域 */
main {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 筛选控件样式 */
.filters-container {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    white-space: nowrap;
}

#exchange-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    min-width: 150px;
}

#symbol-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 200px;
}

.clear-btn {
    padding: 6px 12px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
}

.clear-btn:hover {
    background: #c0392b;
}

.reset-btn {
    padding: 8px 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.reset-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.reset-btn:active {
    transform: translateY(0);
}

.reset-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 消息提示动画 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-data p {
    margin-bottom: 10px;
    font-size: 16px;
}

.no-data .hint {
    font-size: 14px;
    font-style: italic;
}

.no-data code {
    background: #ecf0f1;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

/* 交易所容器网格布局 */
#exchanges-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    padding: 0;
}

/* 大屏幕优化 - 确保能显示更多交易所 */
@media (min-width: 1600px) {
    .container {
        max-width: 1800px;
    }

    #exchanges-container {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 10px;
    }
}

@media (min-width: 1920px) {
    .container {
        max-width: 2000px;
    }

    #exchanges-container {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 8px;
    }
}

/* 交易所卡片样式 */
.exchange-card {
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    margin-bottom: 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.exchange-header {
    background: #f8f9fa;
    padding: 10px 12px;
    border-bottom: 1px solid #e1e8ed;
}

.exchange-name {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
}

.symbol-info {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 1px;
}

.positions-container {
    padding: 12px;
}

/* 持仓条样式 */
.position-bar {
    margin-bottom: 12px;
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.position-side {
    font-weight: 600;
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 3px;
    color: white;
}

.position-side.long {
    background: #27ae60;
}

.position-side.short {
    background: #e74c3c;
}

.position-info {
    font-size: 10px;
    color: #7f8c8d;
}

.progress-container {
    background: #ecf0f1;
    border-radius: 6px;
    height: 20px;
    position: relative;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 9px;
}

.progress-bar.profit {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.progress-bar.loss {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.progress-bar.neutral {
    background: linear-gradient(90deg, #95a5a6, #7f8c8d);
}

.position-details {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 6px;
    font-size: 9px;
    color: #7f8c8d;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 60px;
}

.detail-label {
    font-weight: 500;
    margin-bottom: 1px;
}

.detail-value {
    color: #2c3e50;
    font-weight: 600;
}

/* 时间详情样式 */
.time-details {
    margin-top: 6px;
    padding: 6px;
    background: #f8f9fa;
    border-radius: 3px;
    border-left: 2px solid #3498db;
}

.time-details-header {
    font-size: 9px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 3px;
}

.time-details-content {
    font-size: 8px;
    color: #7f8c8d;
}

.time-item {
    margin-bottom: 1px;
}

.time-label {
    font-weight: 500;
    color: #34495e;
}

/* 底部图例 */
footer {
    margin-top: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #2c3e50;
}

.color-box {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.color-box.profit {
    background: #e74c3c;
}

.color-box.loss {
    background: #27ae60;
}

.color-box.neutral {
    background: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    #exchanges-container {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 10px;
    }

    #exchanges-container {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 8px;
    }

    header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        padding: 12px;
    }

    .status {
        align-items: center;
    }

    .filters-container {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
        padding: 10px;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    #exchange-filter,
    #symbol-filter {
        min-width: auto;
        width: 100%;
    }

    #exchanges-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 8px;
    }

    .position-details {
        flex-wrap: wrap;
        gap: 6px;
    }

    .legend {
        gap: 10px;
    }
}
